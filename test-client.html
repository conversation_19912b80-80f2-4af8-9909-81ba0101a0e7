<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Weather Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>🌤️ MCP Weather Client</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="status" class="status disconnected">未连接</div>
        <button onclick="connect()">连接服务器</button>
        <button onclick="disconnect()">断开连接</button>
    </div>
    
    <div class="container">
        <h2>天气查询</h2>
        <div class="input-group">
            <label for="city">城市名称:</label>
            <input type="text" id="city" value="北京" placeholder="输入城市名称">
        </div>
        <div class="input-group">
            <label for="country">国家代码 (可选):</label>
            <input type="text" id="country" value="CN" placeholder="如: CN, US, UK">
        </div>
        <button onclick="queryWeather()">查询天气</button>
        <button onclick="queryForecast()">查询预报</button>
    </div>
    
    <div class="container">
        <h2>MCP 工具</h2>
        <button onclick="listTools()">获取工具列表</button>
        <button onclick="initialize()">初始化连接</button>
        <button onclick="ping()">发送 Ping</button>
    </div>
    
    <div class="container">
        <h2>服务器日志</h2>
        <div id="log" class="log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let eventSource = null;
        let clientId = 'client-' + Date.now();
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(connected) {
            const statusDiv = document.getElementById('status');
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = `已连接 (客户端ID: ${clientId})`;
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = '未连接';
            }
        }
        
        function connect() {
            if (eventSource) {
                eventSource.close();
            }
            
            log(`正在连接到服务器... (客户端ID: ${clientId})`);
            eventSource = new EventSource(`http://localhost:8080/mcp/events/${clientId}`);
            
            eventSource.onopen = function(event) {
                log('SSE 连接已建立');
                updateStatus(true);
            };
            
            eventSource.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    log(`收到消息: ${JSON.stringify(data, null, 2)}`);
                } catch (e) {
                    log(`收到原始消息: ${event.data}`);
                }
            };
            
            eventSource.onerror = function(event) {
                log('SSE 连接错误');
                updateStatus(false);
            };
        }
        
        function disconnect() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
                log('已断开连接');
                updateStatus(false);
            }
        }
        
        async function sendMcpRequest(method, params = null) {
            const request = {
                jsonrpc: "2.0",
                id: Date.now().toString(),
                method: method,
                params: params
            };
            
            log(`发送请求: ${JSON.stringify(request, null, 2)}`);
            
            try {
                const response = await fetch(`http://localhost:8080/mcp/request/${clientId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(request)
                });
                
                const result = await response.json();
                log(`请求响应: ${JSON.stringify(result, null, 2)}`);
            } catch (error) {
                log(`请求失败: ${error.message}`);
            }
        }
        
        function queryWeather() {
            const city = document.getElementById('city').value;
            const country = document.getElementById('country').value;
            
            if (!city) {
                alert('请输入城市名称');
                return;
            }
            
            const params = {
                name: "get_weather",
                arguments: {
                    city: city,
                    country: country || undefined
                }
            };
            
            sendMcpRequest("tools/call", params);
        }
        
        function queryForecast() {
            const city = document.getElementById('city').value;
            
            if (!city) {
                alert('请输入城市名称');
                return;
            }
            
            const params = {
                name: "get_weather_forecast",
                arguments: {
                    city: city,
                    days: 3
                }
            };
            
            sendMcpRequest("tools/call", params);
        }
        
        function listTools() {
            sendMcpRequest("tools/list");
        }
        
        function initialize() {
            sendMcpRequest("initialize");
        }
        
        function ping() {
            sendMcpRequest("ping");
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            log('页面已加载，点击"连接服务器"开始使用');
        };
        
        // 页面关闭时断开连接
        window.onbeforeunload = function() {
            disconnect();
        };
    </script>
</body>
</html>
