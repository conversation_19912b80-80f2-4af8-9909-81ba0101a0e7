# MCP Weather Server

基于 Spring AI 构建的天气查询 MCP (Model Context Protocol) 服务，支持 stdio 和 SSE 两种通信模式。

## 🚀 技术栈

- **Java 21** - 现代 Java 开发
- **Spring Boot 3.5.4** - 应用框架
- **Spring AI 1.0.0-M4** - AI 集成框架
- **Spring WebFlux** - 响应式 Web 框架
- **Jackson** - JSON 序列化/反序列化
- **Maven** - 项目构建工具

## 🏗️ 项目架构

```
src/main/java/com/mcp/weather/
├── McpWeatherApplication.java          # 主启动类
├── config/
│   └── McpWeatherConfig.java          # 配置类
├── controller/
│   ├── StdioController.java           # stdio 模式控制器
│   └── SseController.java             # SSE 模式控制器
├── model/
│   ├── WeatherInfo.java               # 天气信息模型
│   ├── McpRequest.java                # MCP 请求模型
│   └── McpResponse.java               # MCP 响应模型
└── service/
    ├── WeatherService.java            # 天气查询服务
    └── McpProtocolService.java        # MCP 协议处理服务
```

## ⚙️ 配置说明

### 应用配置 (application.properties)

```properties
# MCP 模式配置
mcp.mode=sse                           # stdio 或 sse

# Spring AI 配置 (可选)
spring.ai.openai.api-key=${OPENAI_API_KEY:}

# 天气 API 配置 (可选)
weather.api.key=${WEATHER_API_KEY:}
weather.mock.enabled=true              # 是否使用模拟数据
```

### 环境变量

- `OPENAI_API_KEY`: OpenAI API 密钥（用于增强天气描述，可选）
- `WEATHER_API_KEY`: OpenWeatherMap API 密钥（用于真实天气数据，可选）

## 🔧 运行方式

### 1. SSE 模式（默认）

```bash
# 编译项目
mvn clean compile

# 运行服务
mvn spring-boot:run

# 或指定 SSE 模式
mvn spring-boot:run -Dspring-boot.run.arguments="--mcp.mode=sse"
```

服务启动后访问：http://localhost:8080

### 2. stdio 模式

```bash
# 运行 stdio 模式
mvn spring-boot:run -Dspring-boot.run.arguments="--mcp.mode=stdio"
```

## 📡 API 接口

### SSE 模式接口

#### 1. 建立 SSE 连接
```
GET /mcp/events/{clientId}
Content-Type: text/event-stream
```

#### 2. 发送 MCP 请求
```
POST /mcp/request/{clientId}
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "tools/call",
  "params": {
    "name": "get_weather",
    "arguments": {
      "city": "北京",
      "country": "CN"
    }
  }
}
```

#### 3. 直接查询天气
```
GET /mcp/weather?city=北京&country=CN
```

#### 4. 获取服务状态
```
GET /mcp/status
```

#### 5. 获取可用工具
```
GET /mcp/tools
```

## 🛠️ 支持的 MCP 方法

### 1. tools/list
获取可用工具列表

**请求示例：**
```json
{
  "jsonrpc": "2.0",
  "id": "1",
  "method": "tools/list"
}
```

### 2. tools/call - get_weather
查询指定城市的天气信息

**请求示例：**
```json
{
  "jsonrpc": "2.0",
  "id": "2",
  "method": "tools/call",
  "params": {
    "name": "get_weather",
    "arguments": {
      "city": "上海",
      "country": "CN",
      "units": "metric"
    }
  }
}
```

### 3. tools/call - get_weather_forecast
获取天气预报（模拟实现）

**请求示例：**
```json
{
  "jsonrpc": "2.0",
  "id": "3",
  "method": "tools/call",
  "params": {
    "name": "get_weather_forecast",
    "arguments": {
      "city": "广州",
      "days": 3
    }
  }
}
```

### 4. initialize
初始化 MCP 连接

**请求示例：**
```json
{
  "jsonrpc": "2.0",
  "id": "init",
  "method": "initialize"
}
```

### 5. ping
心跳检测

**请求示例：**
```json
{
  "jsonrpc": "2.0",
  "id": "ping",
  "method": "ping"
}
```

## 🌤️ 天气数据源

### 模拟数据模式（默认）
- 生成随机但合理的天气数据
- 支持中英文城市名称
- 无需外部 API 密钥

### OpenWeatherMap API 模式
1. 注册 [OpenWeatherMap](https://openweathermap.org/api) 账号
2. 获取 API 密钥
3. 设置环境变量：`export WEATHER_API_KEY=your_api_key`
4. 设置配置：`weather.mock.enabled=false`

## 🤖 Spring AI 集成

### 功能特性
- 使用 Spring AI 增强天气描述
- 支持多种 AI 模型（OpenAI GPT、Azure OpenAI 等）
- 智能生成友好的天气报告和出行建议

### 配置示例
```properties
# 使用 OpenAI
spring.ai.openai.api-key=your_openai_key
spring.ai.openai.chat.options.model=gpt-3.5-turbo

# 使用 Azure OpenAI
spring.ai.azure.openai.api-key=your_azure_key
spring.ai.azure.openai.endpoint=your_azure_endpoint
```

## 📝 使用示例

### 1. 使用 curl 测试 SSE 模式

```bash
# 启动服务
mvn spring-boot:run

# 建立 SSE 连接（在另一个终端）
curl -N -H "Accept: text/event-stream" http://localhost:8080/mcp/events/client1

# 发送天气查询请求（在第三个终端）
curl -X POST http://localhost:8080/mcp/request/client1 \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": "weather1",
    "method": "tools/call",
    "params": {
      "name": "get_weather",
      "arguments": {
        "city": "北京"
      }
    }
  }'

# 直接查询天气
curl "http://localhost:8080/mcp/weather?city=上海&country=CN"
```

### 2. 使用 stdio 模式

```bash
# 启动 stdio 模式
mvn spring-boot:run -Dspring-boot.run.arguments="--mcp.mode=stdio"

# 在标准输入中输入 JSON 请求
{"jsonrpc":"2.0","id":"1","method":"tools/call","params":{"name":"get_weather","arguments":{"city":"深圳"}}}
```

## 🔍 日志和调试

### 日志级别配置
```properties
logging.level.com.mcp.weather=DEBUG
logging.level.org.springframework.ai=DEBUG
```

### 常见问题排查
1. **Spring AI 不工作**: 检查 API 密钥配置
2. **真实天气数据获取失败**: 检查网络连接和 API 密钥
3. **SSE 连接断开**: 检查防火墙和代理设置

## 🚀 扩展功能

### 添加新的天气工具
1. 在 `McpProtocolService.handleToolsList()` 中添加工具定义
2. 在 `McpProtocolService.handleToolsCall()` 中添加处理逻辑
3. 在 `WeatherService` 中实现具体功能

### 集成其他 AI 模型
1. 添加相应的 Spring AI starter 依赖
2. 配置模型参数
3. 修改 `WeatherService.enhanceWeatherDescription()` 方法

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
