#!/bin/bash

echo "Starting MCP Weather Server in SSE mode..."
echo "Server will be available at: http://localhost:8080"
echo ""
echo "Available endpoints:"
echo "  - GET  /mcp/status           - Server status"
echo "  - GET  /mcp/tools            - Available tools"
echo "  - GET  /mcp/weather?city=北京 - Direct weather query"
echo "  - GET  /mcp/events/{clientId} - SSE connection"
echo "  - POST /mcp/request/{clientId} - MCP request"
echo ""

mvn spring-boot:run -Dspring-boot.run.arguments="--mcp.mode=sse"
