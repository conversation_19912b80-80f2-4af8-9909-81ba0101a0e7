package com.mcp.weather.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mcp.weather.model.McpRequest;
import com.mcp.weather.service.McpProtocolService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;

@WebFluxTest(SseController.class)
@TestPropertySource(properties = {
    "mcp.mode=sse",
    "weather.mock.enabled=true"
})
class SseControllerTest {
    
    @Autowired
    private WebTestClient webTestClient;
    
    @MockBean
    private McpProtocolService mcpProtocolService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void testGetStatus() {
        webTestClient.get()
            .uri("/mcp/status")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.status").isEqualTo("running")
            .jsonPath("$.mode").isEqualTo("sse")
            .jsonPath("$.connectedClients").isNumber()
            .jsonPath("$.timestamp").isNumber()
            .jsonPath("$.version").isEqualTo("1.0.0");
    }
    
    @Test
    void testGetWeather() {
        webTestClient.get()
            .uri("/mcp/weather?city=北京&country=CN")
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.success").exists();
    }
    
    @Test
    void testGetWeather_MissingCity() {
        webTestClient.get()
            .uri("/mcp/weather")
            .exchange()
            .expectStatus().isBadRequest();
    }
    
    @Test
    void testStreamEvents() {
        webTestClient.get()
            .uri("/mcp/events/test-client")
            .accept(MediaType.TEXT_EVENT_STREAM)
            .exchange()
            .expectStatus().isOk()
            .expectHeader().contentType(MediaType.TEXT_EVENT_STREAM);
    }
    
    @Test
    void testHandleRequest() {
        McpRequest.WeatherQueryArgs args = new McpRequest.WeatherQueryArgs("北京", "CN");
        McpRequest.McpParams params = new McpRequest.McpParams("get_weather", args);
        McpRequest request = new McpRequest("test-1", "tools/call", params);
        
        webTestClient.post()
            .uri("/mcp/request/test-client")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(request)
            .exchange()
            .expectStatus().isOk()
            .expectBody()
            .jsonPath("$.status").exists()
            .jsonPath("$.requestId").isEqualTo("test-1");
    }
}
