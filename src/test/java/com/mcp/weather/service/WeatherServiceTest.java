package com.mcp.weather.service;

import com.mcp.weather.model.WeatherInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
    "weather.mock.enabled=true",
    "mcp.mode=sse"
})
class WeatherServiceTest {
    
    @MockBean
    private ChatClient.Builder chatClientBuilder;
    
    @MockBean
    private WebClient.Builder webClientBuilder;
    
    private WeatherService weatherService;
    
    @BeforeEach
    void setUp() {
        weatherService = new WeatherService(chatClientBuilder, webClientBuilder);
    }
    
    @Test
    void testGetWeatherInfo_MockMode() {
        // 测试模拟模式下的天气查询
        Mono<WeatherInfo> result = weatherService.getWeatherInfo("北京", "CN");
        
        StepVerifier.create(result)
            .assertNext(weatherInfo -> {
                assertNotNull(weatherInfo);
                assertEquals("北京", weatherInfo.getCity());
                assertEquals("CN", weatherInfo.getCountry());
                assertNotNull(weatherInfo.getTemperature());
                assertNotNull(weatherInfo.getDescription());
                assertNotNull(weatherInfo.getTimestamp());
                
                // 验证温度范围合理
                assertTrue(weatherInfo.getTemperature() >= 15.0);
                assertTrue(weatherInfo.getTemperature() <= 35.0);
                
                // 验证湿度范围合理
                if (weatherInfo.getHumidity() != null) {
                    assertTrue(weatherInfo.getHumidity() >= 30);
                    assertTrue(weatherInfo.getHumidity() <= 80);
                }
            })
            .verifyComplete();
    }
    
    @Test
    void testGetWeatherInfo_DifferentCities() {
        String[] cities = {"上海", "广州", "深圳", "杭州"};
        
        for (String city : cities) {
            Mono<WeatherInfo> result = weatherService.getWeatherInfo(city, "CN");
            
            StepVerifier.create(result)
                .assertNext(weatherInfo -> {
                    assertEquals(city, weatherInfo.getCity());
                    assertNotNull(weatherInfo.getTemperature());
                    assertNotNull(weatherInfo.getDescription());
                })
                .verifyComplete();
        }
    }
    
    @Test
    void testGetWeatherInfo_EnglishCity() {
        Mono<WeatherInfo> result = weatherService.getWeatherInfo("London", "UK");
        
        StepVerifier.create(result)
            .assertNext(weatherInfo -> {
                assertEquals("London", weatherInfo.getCity());
                assertEquals("UK", weatherInfo.getCountry());
                assertNotNull(weatherInfo.getTemperature());
            })
            .verifyComplete();
    }
    
    @Test
    void testGetWeatherInfo_NullCountry() {
        Mono<WeatherInfo> result = weatherService.getWeatherInfo("北京", null);
        
        StepVerifier.create(result)
            .assertNext(weatherInfo -> {
                assertEquals("北京", weatherInfo.getCity());
                assertEquals("CN", weatherInfo.getCountry()); // 默认值
            })
            .verifyComplete();
    }
}
