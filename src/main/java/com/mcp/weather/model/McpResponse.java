package com.mcp.weather.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * MCP 响应模型
 */
public class McpResponse<T> {
    
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("result")
    private T result;
    
    @JsonProperty("error")
    private McpError error;
    
    // 构造函数
    public McpResponse() {}
    
    public McpResponse(String id, T result) {
        this.id = id;
        this.result = result;
    }
    
    public McpResponse(String id, McpError error) {
        this.id = id;
        this.error = error;
    }
    
    // 静态工厂方法
    public static <T> McpResponse<T> success(String id, T result) {
        return new McpResponse<>(id, result);
    }
    
    public static <T> McpResponse<T> error(String id, int code, String message) {
        return new McpResponse<>(id, new McpError(code, message));
    }
    
    public static <T> McpResponse<T> error(String id, int code, String message, Object data) {
        return new McpResponse<>(id, new McpError(code, message, data));
    }
    
    // Getter 和 Setter 方法
    public String getJsonrpc() {
        return jsonrpc;
    }
    
    public void setJsonrpc(String jsonrpc) {
        this.jsonrpc = jsonrpc;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public T getResult() {
        return result;
    }
    
    public void setResult(T result) {
        this.result = result;
    }
    
    public McpError getError() {
        return error;
    }
    
    public void setError(McpError error) {
        this.error = error;
    }
    
    /**
     * MCP 错误信息
     */
    public static class McpError {
        
        @JsonProperty("code")
        private int code;
        
        @JsonProperty("message")
        private String message;
        
        @JsonProperty("data")
        private Object data;
        
        // 构造函数
        public McpError() {}
        
        public McpError(int code, String message) {
            this.code = code;
            this.message = message;
        }
        
        public McpError(int code, String message, Object data) {
            this.code = code;
            this.message = message;
            this.data = data;
        }
        
        // Getter 和 Setter 方法
        public int getCode() {
            return code;
        }
        
        public void setCode(int code) {
            this.code = code;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public Object getData() {
            return data;
        }
        
        public void setData(Object data) {
            this.data = data;
        }
    }
    
    /**
     * 工具调用结果
     */
    public static class ToolCallResult {
        
        @JsonProperty("content")
        private Object content;
        
        @JsonProperty("isError")
        private boolean isError = false;
        
        // 构造函数
        public ToolCallResult() {}
        
        public ToolCallResult(Object content) {
            this.content = content;
        }
        
        public ToolCallResult(Object content, boolean isError) {
            this.content = content;
            this.isError = isError;
        }
        
        // Getter 和 Setter 方法
        public Object getContent() {
            return content;
        }
        
        public void setContent(Object content) {
            this.content = content;
        }
        
        public boolean isError() {
            return isError;
        }
        
        public void setError(boolean error) {
            isError = error;
        }
    }
}
