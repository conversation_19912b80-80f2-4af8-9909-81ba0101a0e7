package com.mcp.weather.model;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 天气信息模型
 */
public class WeatherInfo {
    
    @JsonProperty("city")
    private String city;
    
    @JsonProperty("country")
    private String country;
    
    @JsonProperty("temperature")
    private Double temperature;
    
    @JsonProperty("feels_like")
    private Double feelsLike;
    
    @JsonProperty("humidity")
    private Integer humidity;
    
    @JsonProperty("pressure")
    private Integer pressure;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("wind_speed")
    private Double windSpeed;
    
    @JsonProperty("wind_direction")
    private Integer windDirection;
    
    @JsonProperty("visibility")
    private Integer visibility;
    
    @JsonProperty("uv_index")
    private Double uvIndex;
    
    @JsonProperty("timestamp")
    private Long timestamp;
    
    // 构造函数
    public WeatherInfo() {}
    
    public WeatherInfo(String city, String country, Double temperature, String description) {
        this.city = city;
        this.country = country;
        this.temperature = temperature;
        this.description = description;
        this.timestamp = System.currentTimeMillis();
    }
    
    // Getter 和 Setter 方法
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getCountry() {
        return country;
    }
    
    public void setCountry(String country) {
        this.country = country;
    }
    
    public Double getTemperature() {
        return temperature;
    }
    
    public void setTemperature(Double temperature) {
        this.temperature = temperature;
    }
    
    public Double getFeelsLike() {
        return feelsLike;
    }
    
    public void setFeelsLike(Double feelsLike) {
        this.feelsLike = feelsLike;
    }
    
    public Integer getHumidity() {
        return humidity;
    }
    
    public void setHumidity(Integer humidity) {
        this.humidity = humidity;
    }
    
    public Integer getPressure() {
        return pressure;
    }
    
    public void setPressure(Integer pressure) {
        this.pressure = pressure;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Double getWindSpeed() {
        return windSpeed;
    }
    
    public void setWindSpeed(Double windSpeed) {
        this.windSpeed = windSpeed;
    }
    
    public Integer getWindDirection() {
        return windDirection;
    }
    
    public void setWindDirection(Integer windDirection) {
        this.windDirection = windDirection;
    }
    
    public Integer getVisibility() {
        return visibility;
    }
    
    public void setVisibility(Integer visibility) {
        this.visibility = visibility;
    }
    
    public Double getUvIndex() {
        return uvIndex;
    }
    
    public void setUvIndex(Double uvIndex) {
        this.uvIndex = uvIndex;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    @Override
    public String toString() {
        return String.format("WeatherInfo{city='%s', country='%s', temperature=%.1f°C, description='%s', humidity=%d%%, pressure=%d hPa}", 
                city, country, temperature, description, humidity, pressure);
    }
}
