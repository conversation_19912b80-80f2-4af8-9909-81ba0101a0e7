package com.mcp.weather.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;

/**
 * MCP 请求模型
 */
public class McpRequest {
    
    @JsonProperty("jsonrpc")
    private String jsonrpc = "2.0";
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("method")
    @NotBlank(message = "Method cannot be blank")
    private String method;
    
    @JsonProperty("params")
    private McpParams params;
    
    // 构造函数
    public McpRequest() {}
    
    public McpRequest(String id, String method, McpParams params) {
        this.id = id;
        this.method = method;
        this.params = params;
    }
    
    // Getter 和 Setter 方法
    public String getJsonrpc() {
        return jsonrpc;
    }
    
    public void setJsonrpc(String jsonrpc) {
        this.jsonrpc = jsonrpc;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getMethod() {
        return method;
    }
    
    public void setMethod(String method) {
        this.method = method;
    }
    
    public McpParams getParams() {
        return params;
    }
    
    public void setParams(McpParams params) {
        this.params = params;
    }
    
    /**
     * MCP 请求参数
     */
    public static class McpParams {
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("arguments")
        private Object arguments;
        
        // 构造函数
        public McpParams() {}
        
        public McpParams(String name, Object arguments) {
            this.name = name;
            this.arguments = arguments;
        }
        
        // Getter 和 Setter 方法
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public Object getArguments() {
            return arguments;
        }
        
        public void setArguments(Object arguments) {
            this.arguments = arguments;
        }
    }
    
    /**
     * 天气查询参数
     */
    public static class WeatherQueryArgs {
        
        @JsonProperty("city")
        @NotBlank(message = "City name cannot be blank")
        private String city;
        
        @JsonProperty("country")
        private String country;
        
        @JsonProperty("units")
        private String units = "metric"; // metric, imperial, kelvin
        
        // 构造函数
        public WeatherQueryArgs() {}
        
        public WeatherQueryArgs(String city) {
            this.city = city;
        }
        
        public WeatherQueryArgs(String city, String country) {
            this.city = city;
            this.country = country;
        }
        
        // Getter 和 Setter 方法
        public String getCity() {
            return city;
        }
        
        public void setCity(String city) {
            this.city = city;
        }
        
        public String getCountry() {
            return country;
        }
        
        public void setCountry(String country) {
            this.country = country;
        }
        
        public String getUnits() {
            return units;
        }
        
        public void setUnits(String units) {
            this.units = units;
        }
    }
}
