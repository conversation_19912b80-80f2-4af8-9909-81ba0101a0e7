package com.mcp.weather.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mcp.weather.model.McpRequest;
import com.mcp.weather.model.McpResponse;
import com.mcp.weather.model.WeatherInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * MCP 协议处理服务
 */
@Service
public class McpProtocolService {
    
    private static final Logger logger = LoggerFactory.getLogger(McpProtocolService.class);
    
    private final WeatherService weatherService;
    private final ObjectMapper objectMapper;
    
    // MCP 错误代码常量
    private static final int PARSE_ERROR = -32700;
    private static final int INVALID_REQUEST = -32600;
    private static final int METHOD_NOT_FOUND = -32601;
    private static final int INVALID_PARAMS = -32602;
    private static final int INTERNAL_ERROR = -32603;
    
    public McpProtocolService(WeatherService weatherService, ObjectMapper objectMapper) {
        this.weatherService = weatherService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 处理 MCP 请求
     */
    public Mono<McpResponse<?>> handleRequest(McpRequest request) {
        logger.info("Handling MCP request: method={}, id={}", request.getMethod(), request.getId());
        
        try {
            return switch (request.getMethod()) {
                case "tools/list" -> handleToolsList(request);
                case "tools/call" -> handleToolsCall(request);
                case "initialize" -> handleInitialize(request);
                case "ping" -> handlePing(request);
                default -> Mono.just(McpResponse.error(request.getId(), METHOD_NOT_FOUND, 
                    "Method not found: " + request.getMethod()));
            };
        } catch (Exception e) {
            logger.error("Error handling MCP request", e);
            return Mono.just(McpResponse.error(request.getId(), INTERNAL_ERROR, 
                "Internal server error: " + e.getMessage()));
        }
    }
    
    /**
     * 处理工具列表请求
     */
    private Mono<McpResponse<?>> handleToolsList(McpRequest request) {
        Map<String, Object> tools = Map.of(
            "tools", List.of(
                Map.of(
                    "name", "get_weather",
                    "description", "获取指定城市的天气信息",
                    "inputSchema", Map.of(
                        "type", "object",
                        "properties", Map.of(
                            "city", Map.of(
                                "type", "string",
                                "description", "城市名称（中文或英文）"
                            ),
                            "country", Map.of(
                                "type", "string",
                                "description", "国家代码（可选），如 CN, US 等"
                            ),
                            "units", Map.of(
                                "type", "string",
                                "description", "温度单位（可选）：metric（摄氏度）, imperial（华氏度）, kelvin（开尔文）",
                                "default", "metric"
                            )
                        ),
                        "required", List.of("city")
                    )
                ),
                Map.of(
                    "name", "get_weather_forecast",
                    "description", "获取指定城市的天气预报（未来几天）",
                    "inputSchema", Map.of(
                        "type", "object",
                        "properties", Map.of(
                            "city", Map.of(
                                "type", "string",
                                "description", "城市名称（中文或英文）"
                            ),
                            "days", Map.of(
                                "type", "integer",
                                "description", "预报天数（1-7天）",
                                "default", 3
                            )
                        ),
                        "required", List.of("city")
                    )
                )
            )
        );
        
        return Mono.just(McpResponse.success(request.getId(), tools));
    }
    
    /**
     * 处理工具调用请求
     */
    private Mono<McpResponse<?>> handleToolsCall(McpRequest request) {
        if (request.getParams() == null || request.getParams().getName() == null) {
            return Mono.just(McpResponse.error(request.getId(), INVALID_PARAMS, 
                "Missing tool name in params"));
        }
        
        String toolName = request.getParams().getName();
        Object arguments = request.getParams().getArguments();
        
        return switch (toolName) {
            case "get_weather" -> handleGetWeather(request.getId(), arguments);
            case "get_weather_forecast" -> handleGetWeatherForecast(request.getId(), arguments);
            default -> Mono.just(McpResponse.error(request.getId(), METHOD_NOT_FOUND, 
                "Tool not found: " + toolName));
        };
    }
    
    /**
     * 处理获取天气请求
     */
    private Mono<McpResponse<?>> handleGetWeather(String requestId, Object arguments) {
        try {
            McpRequest.WeatherQueryArgs args = objectMapper.convertValue(arguments, 
                McpRequest.WeatherQueryArgs.class);
            
            if (args.getCity() == null || args.getCity().trim().isEmpty()) {
                return Mono.just(McpResponse.error(requestId, INVALID_PARAMS, 
                    "City name is required"));
            }
            
            return weatherService.getWeatherInfo(args.getCity().trim(), args.getCountry())
                .flatMap(weatherService::enhanceWeatherDescription)
                .map(weatherInfo -> {
                    McpResponse.ToolCallResult result = new McpResponse.ToolCallResult(
                        List.of(Map.of(
                            "type", "text",
                            "text", formatWeatherResponse(weatherInfo)
                        ))
                    );
                    return McpResponse.success(requestId, result);
                })
                .onErrorResume(error -> {
                    logger.error("Error getting weather info", error);
                    McpResponse.ToolCallResult errorResult = new McpResponse.ToolCallResult(
                        List.of(Map.of(
                            "type", "text",
                            "text", "获取天气信息失败: " + error.getMessage()
                        )),
                        true
                    );
                    return Mono.just(McpResponse.success(requestId, errorResult));
                });
                
        } catch (Exception e) {
            logger.error("Error parsing weather query arguments", e);
            return Mono.just(McpResponse.error(requestId, INVALID_PARAMS, 
                "Invalid arguments: " + e.getMessage()));
        }
    }
    
    /**
     * 处理获取天气预报请求（模拟实现）
     */
    private Mono<McpResponse<?>> handleGetWeatherForecast(String requestId, Object arguments) {
        try {
            Map<String, Object> args = objectMapper.convertValue(arguments, Map.class);
            String city = (String) args.get("city");
            Integer days = args.get("days") != null ? (Integer) args.get("days") : 3;
            
            if (city == null || city.trim().isEmpty()) {
                return Mono.just(McpResponse.error(requestId, INVALID_PARAMS, 
                    "City name is required"));
            }
            
            // 模拟天气预报数据
            String forecastText = String.format("未来%d天%s的天气预报：\n", days, city);
            for (int i = 1; i <= days; i++) {
                forecastText += String.format("第%d天：晴转多云，气温 %d-%d°C，湿度 %d%%\n", 
                    i, 15 + i, 25 + i, 50 + i * 5);
            }
            
            McpResponse.ToolCallResult result = new McpResponse.ToolCallResult(
                List.of(Map.of(
                    "type", "text",
                    "text", forecastText
                ))
            );
            
            return Mono.just(McpResponse.success(requestId, result));
            
        } catch (Exception e) {
            logger.error("Error parsing weather forecast arguments", e);
            return Mono.just(McpResponse.error(requestId, INVALID_PARAMS, 
                "Invalid arguments: " + e.getMessage()));
        }
    }
    
    /**
     * 处理初始化请求
     */
    private Mono<McpResponse<?>> handleInitialize(McpRequest request) {
        Map<String, Object> capabilities = Map.of(
            "protocolVersion", "2024-11-05",
            "capabilities", Map.of(
                "tools", Map.of(
                    "listChanged", false
                )
            ),
            "serverInfo", Map.of(
                "name", "mcp-weather-server",
                "version", "1.0.0"
            )
        );
        
        return Mono.just(McpResponse.success(request.getId(), capabilities));
    }
    
    /**
     * 处理 ping 请求
     */
    private Mono<McpResponse<?>> handlePing(McpRequest request) {
        return Mono.just(McpResponse.success(request.getId(), Map.of("status", "pong")));
    }
    
    /**
     * 格式化天气响应
     */
    private String formatWeatherResponse(WeatherInfo weather) {
        StringBuilder sb = new StringBuilder();
        sb.append(String.format("🌤️ %s天气信息\n", weather.getCity()));
        sb.append(String.format("🌡️ 温度：%.1f°C", weather.getTemperature()));
        
        if (weather.getFeelsLike() != null) {
            sb.append(String.format("（体感温度：%.1f°C）", weather.getFeelsLike()));
        }
        sb.append("\n");
        
        sb.append(String.format("☁️ 天气：%s\n", weather.getDescription()));
        
        if (weather.getHumidity() != null) {
            sb.append(String.format("💧 湿度：%d%%\n", weather.getHumidity()));
        }
        
        if (weather.getPressure() != null) {
            sb.append(String.format("🌪️ 气压：%d hPa\n", weather.getPressure()));
        }
        
        if (weather.getWindSpeed() != null) {
            sb.append(String.format("💨 风速：%.1f m/s", weather.getWindSpeed()));
            if (weather.getWindDirection() != null) {
                sb.append(String.format("（%d°）", weather.getWindDirection()));
            }
            sb.append("\n");
        }
        
        if (weather.getVisibility() != null) {
            sb.append(String.format("👁️ 能见度：%.1f km\n", weather.getVisibility() / 1000.0));
        }
        
        sb.append(String.format("\n📅 更新时间：%s", 
            new java.util.Date(weather.getTimestamp()).toString()));
        
        return sb.toString();
    }
}
