package com.mcp.weather.service;

import com.mcp.weather.model.WeatherInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Random;

/**
 * 天气查询服务
 */
@Service
public class WeatherService {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherService.class);
    
    private final ChatClient chatClient;
    private final WebClient webClient;
    private final Random random = new Random();
    
    @Value("${weather.api.key:}")
    private String weatherApiKey;
    
    @Value("${weather.api.url:https://api.openweathermap.org/data/2.5/weather}")
    private String weatherApiUrl;
    
    @Value("${weather.mock.enabled:true}")
    private boolean mockEnabled;
    
    public WeatherService(ChatClient.Builder chatClientBuilder, WebClient.Builder webClientBuilder) {
        this.chatClient = chatClientBuilder.build();
        this.webClient = webClientBuilder.build();
    }
    
    /**
     * 查询天气信息
     */
    public Mono<WeatherInfo> getWeatherInfo(String city, String country) {
        logger.info("Querying weather for city: {}, country: {}", city, country);
        
        if (mockEnabled || weatherApiKey.isEmpty()) {
            return getMockWeatherInfo(city, country);
        } else {
            return getRealWeatherInfo(city, country);
        }
    }
    
    /**
     * 使用 Spring AI 增强天气信息描述
     */
    public Mono<WeatherInfo> enhanceWeatherDescription(WeatherInfo weatherInfo) {
        String promptText = """
            请为以下天气信息生成一个友好、详细的中文描述：
            城市：{city}
            国家：{country}
            温度：{temperature}°C
            天气：{description}
            湿度：{humidity}%
            气压：{pressure} hPa
            
            请生成一个自然、友好的天气描述，包含出行建议。
            """;
        
        PromptTemplate promptTemplate = new PromptTemplate(promptText);
        Prompt prompt = promptTemplate.create(Map.of(
            "city", weatherInfo.getCity(),
            "country", weatherInfo.getCountry() != null ? weatherInfo.getCountry() : "未知",
            "temperature", weatherInfo.getTemperature(),
            "description", weatherInfo.getDescription(),
            "humidity", weatherInfo.getHumidity() != null ? weatherInfo.getHumidity() : 0,
            "pressure", weatherInfo.getPressure() != null ? weatherInfo.getPressure() : 0
        ));
        
        return Mono.fromCallable(() -> {
            try {
                String enhancedDescription = chatClient.prompt(prompt).call().content();
                weatherInfo.setDescription(enhancedDescription);
                return weatherInfo;
            } catch (Exception e) {
                logger.warn("Failed to enhance weather description with AI: {}", e.getMessage());
                return weatherInfo;
            }
        });
    }
    
    /**
     * 获取真实天气信息（通过 OpenWeatherMap API）
     */
    private Mono<WeatherInfo> getRealWeatherInfo(String city, String country) {
        String query = country != null && !country.isEmpty() ? city + "," + country : city;
        String url = weatherApiUrl + "?q=" + query + "&appid=" + weatherApiKey + "&units=metric&lang=zh_cn";
        
        return webClient.get()
                .uri(url)
                .retrieve()
                .bodyToMono(Map.class)
                .map(this::parseWeatherResponse)
                .onErrorResume(error -> {
                    logger.error("Failed to fetch real weather data: {}", error.getMessage());
                    return getMockWeatherInfo(city, country);
                });
    }
    
    /**
     * 解析天气 API 响应
     */
    @SuppressWarnings("unchecked")
    private WeatherInfo parseWeatherResponse(Map<String, Object> response) {
        WeatherInfo weatherInfo = new WeatherInfo();
        
        // 基本信息
        weatherInfo.setCity((String) response.get("name"));
        
        Map<String, Object> sys = (Map<String, Object>) response.get("sys");
        if (sys != null) {
            weatherInfo.setCountry((String) sys.get("country"));
        }
        
        // 主要天气信息
        Map<String, Object> main = (Map<String, Object>) response.get("main");
        if (main != null) {
            weatherInfo.setTemperature(((Number) main.get("temp")).doubleValue());
            weatherInfo.setFeelsLike(((Number) main.get("feels_like")).doubleValue());
            weatherInfo.setHumidity(((Number) main.get("humidity")).intValue());
            weatherInfo.setPressure(((Number) main.get("pressure")).intValue());
        }
        
        // 天气描述
        Object weatherArray = response.get("weather");
        if (weatherArray instanceof java.util.List) {
            java.util.List<Map<String, Object>> weatherList = (java.util.List<Map<String, Object>>) weatherArray;
            if (!weatherList.isEmpty()) {
                Map<String, Object> weather = weatherList.get(0);
                weatherInfo.setDescription((String) weather.get("description"));
            }
        }
        
        // 风力信息
        Map<String, Object> wind = (Map<String, Object>) response.get("wind");
        if (wind != null) {
            Object speed = wind.get("speed");
            if (speed != null) {
                weatherInfo.setWindSpeed(((Number) speed).doubleValue());
            }
            Object deg = wind.get("deg");
            if (deg != null) {
                weatherInfo.setWindDirection(((Number) deg).intValue());
            }
        }
        
        // 能见度
        Object visibility = response.get("visibility");
        if (visibility != null) {
            weatherInfo.setVisibility(((Number) visibility).intValue());
        }
        
        weatherInfo.setTimestamp(System.currentTimeMillis());
        
        return weatherInfo;
    }
    
    /**
     * 获取模拟天气信息
     */
    private Mono<WeatherInfo> getMockWeatherInfo(String city, String country) {
        logger.info("Using mock weather data for city: {}", city);
        
        WeatherInfo weatherInfo = new WeatherInfo();
        weatherInfo.setCity(city);
        weatherInfo.setCountry(country != null ? country : "CN");
        
        // 生成随机但合理的天气数据
        weatherInfo.setTemperature(15.0 + random.nextDouble() * 20.0); // 15-35°C
        weatherInfo.setFeelsLike(weatherInfo.getTemperature() + (random.nextDouble() - 0.5) * 4); // ±2°C
        weatherInfo.setHumidity(30 + random.nextInt(50)); // 30-80%
        weatherInfo.setPressure(1000 + random.nextInt(50)); // 1000-1050 hPa
        weatherInfo.setWindSpeed(random.nextDouble() * 10); // 0-10 m/s
        weatherInfo.setWindDirection(random.nextInt(360)); // 0-359°
        weatherInfo.setVisibility(5000 + random.nextInt(10000)); // 5-15km
        weatherInfo.setUvIndex(random.nextDouble() * 10); // 0-10
        
        // 随机天气描述
        String[] descriptions = {
            "晴朗", "多云", "阴天", "小雨", "中雨", "大雨", "雷阵雨", 
            "雪", "雾", "霾", "沙尘", "大风"
        };
        weatherInfo.setDescription(descriptions[random.nextInt(descriptions.length)]);
        
        weatherInfo.setTimestamp(System.currentTimeMillis());
        
        return Mono.just(weatherInfo);
    }
}
