package com.mcp.weather.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mcp.weather.model.McpRequest;
import com.mcp.weather.model.McpResponse;
import com.mcp.weather.service.McpProtocolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SSE 模式控制器
 * 通过 Server-Sent Events 与客户端通信
 */
@RestController
@RequestMapping("/mcp")
@ConditionalOnProperty(name = "mcp.mode", havingValue = "sse", matchIfMissing = true)
public class SseController {
    
    private static final Logger logger = LoggerFactory.getLogger(SseController.class);
    
    private final McpProtocolService mcpProtocolService;
    private final ObjectMapper objectMapper;
    
    // 存储客户端连接
    private final Map<String, Sinks.Many<String>> clientSinks = new ConcurrentHashMap<>();
    
    public SseController(McpProtocolService mcpProtocolService, ObjectMapper objectMapper) {
        this.mcpProtocolService = mcpProtocolService;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 建立 SSE 连接
     */
    @GetMapping(value = "/events/{clientId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamEvents(@PathVariable String clientId) {
        logger.info("Client {} connected via SSE", clientId);
        
        Sinks.Many<String> sink = Sinks.many().multicast().onBackpressureBuffer();
        clientSinks.put(clientId, sink);
        
        // 发送连接确认
        try {
            Map<String, Object> connectEvent = Map.of(
                "type", "connection",
                "status", "connected",
                "clientId", clientId,
                "timestamp", System.currentTimeMillis()
            );
            String eventData = objectMapper.writeValueAsString(connectEvent);
            sink.tryEmitNext("data: " + eventData + "\n\n");
        } catch (Exception e) {
            logger.error("Error sending connection event", e);
        }
        
        return sink.asFlux()
            .doOnCancel(() -> {
                logger.info("Client {} disconnected", clientId);
                clientSinks.remove(clientId);
            })
            .doOnError(error -> {
                logger.error("Error in SSE stream for client {}", clientId, error);
                clientSinks.remove(clientId);
            })
            // 添加心跳保持连接
            .mergeWith(Flux.interval(Duration.ofSeconds(30))
                .map(tick -> "data: {\"type\":\"heartbeat\",\"timestamp\":" + System.currentTimeMillis() + "}\n\n"));
    }
    
    /**
     * 处理 MCP 请求
     */
    @PostMapping("/request/{clientId}")
    public Mono<ResponseEntity<Map<String, Object>>> handleRequest(
            @PathVariable String clientId,
            @RequestBody McpRequest request) {
        
        logger.info("Received MCP request from client {}: method={}, id={}", 
            clientId, request.getMethod(), request.getId());
        
        return mcpProtocolService.handleRequest(request)
            .flatMap(response -> {
                // 通过 SSE 发送响应
                return sendEventToClient(clientId, response)
                    .then(Mono.just(ResponseEntity.ok(Map.of(
                        "status", "accepted",
                        "requestId", request.getId(),
                        "message", "Request processed, response sent via SSE"
                    ))));
            })
            .onErrorResume(error -> {
                logger.error("Error processing MCP request", error);
                
                McpResponse<?> errorResponse = McpResponse.error(request.getId(), -32603, 
                    "Internal server error: " + error.getMessage());
                
                return sendEventToClient(clientId, errorResponse)
                    .then(Mono.just(ResponseEntity.ok(Map.of(
                        "status", "error",
                        "requestId", request.getId(),
                        "message", "Request failed, error sent via SSE"
                    ))));
            });
    }
    
    /**
     * 直接查询天气（简化接口）
     */
    @GetMapping("/weather")
    public Mono<ResponseEntity<Map<String, Object>>> getWeather(
            @RequestParam String city,
            @RequestParam(required = false) String country,
            @RequestParam(required = false, defaultValue = "default") String clientId) {
        
        logger.info("Direct weather query: city={}, country={}", city, country);
        
        // 构造 MCP 请求
        McpRequest.WeatherQueryArgs args = new McpRequest.WeatherQueryArgs(city, country);
        McpRequest.McpParams params = new McpRequest.McpParams("get_weather", args);
        McpRequest request = new McpRequest("weather-" + System.currentTimeMillis(), "tools/call", params);
        
        return mcpProtocolService.handleRequest(request)
            .map(response -> {
                if (response.getError() != null) {
                    return ResponseEntity.badRequest().body(Map.of(
                        "error", response.getError().getMessage(),
                        "code", response.getError().getCode()
                    ));
                } else {
                    return ResponseEntity.ok(Map.of(
                        "success", true,
                        "data", response.getResult()
                    ));
                }
            })
            .onErrorResume(error -> {
                logger.error("Error in direct weather query", error);
                return Mono.just(ResponseEntity.internalServerError().body(Map.of(
                    "error", "Internal server error: " + error.getMessage()
                )));
            });
    }
    
    /**
     * 获取服务器状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getStatus() {
        return ResponseEntity.ok(Map.of(
            "status", "running",
            "mode", "sse",
            "connectedClients", clientSinks.size(),
            "timestamp", System.currentTimeMillis(),
            "version", "1.0.0"
        ));
    }
    
    /**
     * 获取可用工具列表
     */
    @GetMapping("/tools")
    public Mono<ResponseEntity<Map<String, Object>>> getTools() {
        McpRequest request = new McpRequest("tools-list", "tools/list", null);
        
        return mcpProtocolService.handleRequest(request)
            .map(response -> {
                if (response.getError() != null) {
                    return ResponseEntity.badRequest().body(Map.of(
                        "error", response.getError().getMessage()
                    ));
                } else {
                    return ResponseEntity.ok((Map<String, Object>) response.getResult());
                }
            });
    }
    
    /**
     * 向指定客户端发送事件
     */
    private Mono<Void> sendEventToClient(String clientId, Object data) {
        Sinks.Many<String> sink = clientSinks.get(clientId);
        if (sink == null) {
            logger.warn("Client {} not found, cannot send event", clientId);
            return Mono.empty();
        }
        
        try {
            String eventData = objectMapper.writeValueAsString(data);
            sink.tryEmitNext("data: " + eventData + "\n\n");
            return Mono.empty();
        } catch (Exception e) {
            logger.error("Error sending event to client {}", clientId, e);
            return Mono.error(e);
        }
    }
    
    /**
     * 广播事件到所有客户端
     */
    private void broadcastEvent(Object data) {
        try {
            String eventData = objectMapper.writeValueAsString(data);
            String sseData = "data: " + eventData + "\n\n";
            
            clientSinks.values().forEach(sink -> {
                sink.tryEmitNext(sseData);
            });
        } catch (Exception e) {
            logger.error("Error broadcasting event", e);
        }
    }
}
