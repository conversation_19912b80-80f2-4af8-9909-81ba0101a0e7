package com.mcp.weather.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mcp.weather.model.McpRequest;
import com.mcp.weather.model.McpResponse;
import com.mcp.weather.service.McpProtocolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.util.concurrent.CompletableFuture;

/**
 * stdio 模式控制器
 * 通过标准输入输出与客户端通信
 */
@Component
@ConditionalOnProperty(name = "mcp.mode", havingValue = "stdio", matchIfMissing = false)
public class StdioController implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(StdioController.class);
    
    private final McpProtocolService mcpProtocolService;
    private final ObjectMapper objectMapper;
    
    public StdioController(McpProtocolService mcpProtocolService, ObjectMapper objectMapper) {
        this.mcpProtocolService = mcpProtocolService;
        this.objectMapper = objectMapper;
    }
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("Starting MCP Weather Server in stdio mode");
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(System.in));
             PrintWriter writer = new PrintWriter(System.out, true)) {
            
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.trim().isEmpty()) {
                    continue;
                }
                
                try {
                    // 解析 JSON-RPC 请求
                    McpRequest request = objectMapper.readValue(line, McpRequest.class);
                    logger.debug("Received request: {}", line);
                    
                    // 处理请求
                    CompletableFuture<McpResponse<?>> responseFuture = mcpProtocolService
                        .handleRequest(request)
                        .toFuture();
                    
                    McpResponse<?> response = responseFuture.get();
                    
                    // 发送响应
                    String responseJson = objectMapper.writeValueAsString(response);
                    writer.println(responseJson);
                    writer.flush();
                    
                    logger.debug("Sent response: {}", responseJson);
                    
                } catch (Exception e) {
                    logger.error("Error processing request: {}", line, e);
                    
                    // 发送错误响应
                    McpResponse<?> errorResponse = McpResponse.error("unknown", -32603, 
                        "Internal server error: " + e.getMessage());
                    String errorJson = objectMapper.writeValueAsString(errorResponse);
                    writer.println(errorJson);
                    writer.flush();
                }
            }
            
        } catch (Exception e) {
            logger.error("Error in stdio mode", e);
            throw e;
        }
        
        logger.info("MCP Weather Server stdio mode stopped");
    }
}
