# ??????
spring.application.name=mcp-weather
server.port=8080

# MCP ????
# ???: stdio, sse
# stdio: ??????????????????
# sse: Server-Sent Events ????? Web ???
mcp.mode=sse

# Spring AI ??
# OpenAI API ???????????????
spring.ai.openai.api-key=${OPENAI_API_KEY:}
spring.ai.openai.chat.options.model=gpt-3.5-turbo
spring.ai.openai.chat.options.temperature=0.7

# ?? API ??
# OpenWeatherMap API ?????????????????
weather.api.key=${WEATHER_API_KEY:}
weather.api.url=https://api.openweathermap.org/data/2.5/weather
weather.mock.enabled=true

# ????
logging.level.com.mcp.weather=INFO
logging.level.org.springframework.ai=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Web ??
spring.mvc.async.request-timeout=30000
spring.webflux.multipart.max-in-memory-size=1MB

# Jackson ??
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.write-dates-as-timestamps=false
