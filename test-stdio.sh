#!/bin/bash

echo "Testing MCP Weather Server stdio mode..."

# 创建测试输入文件
cat > test-input.json << 'EOF'
{"jsonrpc":"2.0","id":"init","method":"initialize"}
{"jsonrpc":"2.0","id":"tools","method":"tools/list"}
{"jsonrpc":"2.0","id":"weather1","method":"tools/call","params":{"name":"get_weather","arguments":{"city":"北京","country":"CN"}}}
{"jsonrpc":"2.0","id":"weather2","method":"tools/call","params":{"name":"get_weather","arguments":{"city":"上海"}}}
{"jsonrpc":"2.0","id":"forecast","method":"tools/call","params":{"name":"get_weather_forecast","arguments":{"city":"广州","days":3}}}
{"jsonrpc":"2.0","id":"ping","method":"ping"}
EOF

echo "Test input file created: test-input.json"
echo ""
echo "To test stdio mode, run:"
echo "  mvn spring-boot:run -Dspring-boot.run.arguments=\"--mcp.mode=stdio\" < test-input.json"
echo ""
echo "Or start the server and paste the following requests one by one:"
echo ""
cat test-input.json
